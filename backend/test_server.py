#!/usr/bin/env python3
"""
Simple test script to debug server startup issues
"""

import sys
import os
from pathlib import Path

# Add src to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

print("🔍 Testing server components...")

try:
    print("1. Testing graph import...")
    from agent.graph import graph
    print("   ✅ Graph imported successfully")
except Exception as e:
    print(f"   ❌ Graph import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("2. Testing app import...")
    from agent.app import app
    print("   ✅ App imported successfully")
    
    print("3. Testing app routes...")
    routes = []
    for route in app.routes:
        routes.append(route.path)
    print(f"   ✅ Found {len(routes)} routes")
    
except Exception as e:
    print(f"   ❌ App import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("4. Testing uvicorn import...")
    import uvicorn
    print("   ✅ Uvicorn imported successfully")
    
    print("5. Starting server...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    
except Exception as e:
    print(f"   ❌ Server start failed: {e}")
    import traceback
    traceback.print_exc()
