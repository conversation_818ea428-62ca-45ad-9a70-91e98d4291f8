#!/usr/bin/env python3
"""
Startup script for the Power Plant Information Extraction API

This script starts the FastAPI server with proper configuration for the
power plant information extraction pipeline.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Try to import and load dotenv
try:
    from dotenv import load_dotenv
    dotenv_available = True
except ImportError:
    print("⚠️ python-dotenv not installed. Install with: pip install python-dotenv")
    dotenv_available = False

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Load environment variables from .env file
if dotenv_available:
    env_file = current_dir / ".env"
    if env_file.exists():
        print(f"📄 Loading environment variables from: {env_file}")
        load_dotenv(env_file)
        print("✅ Environment variables loaded from .env file")

        # Debug: Check if key variables were loaded
        gemini_key = os.getenv("GEMINI_API_KEY")
        aws_key = os.getenv("AWS_ACCESS_KEY_ID")
        print(f"🔍 Debug: GEMINI_API_KEY loaded: {'Yes' if gemini_key else 'No'}")
        print(f"🔍 Debug: AWS_ACCESS_KEY_ID loaded: {'Yes' if aws_key else 'No'}")
    else:
        print(f"⚠️ No .env file found at: {env_file}")
        print("   Looking for environment variables in system environment...")
else:
    print("   Looking for environment variables in system environment...")

def main():
    """Main function to start the API server"""
    print("🚀 Starting Power Plant Information Extraction API...")
    print("=" * 60)
    
    # Check environment variables
    required_env_vars = [
        "GEMINI_API_KEY",
        "AWS_ACCESS_KEY_ID", 
        "AWS_SECRET_ACCESS_KEY"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables before starting the API.")
        sys.exit(1)
    
    print("✅ Environment variables check passed")
    
    # Configuration
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))  # Back to default port 8000
    reload = os.getenv("API_RELOAD", "false").lower() == "true"
    log_level = os.getenv("API_LOG_LEVEL", "info").lower()
    
    print(f"📋 Configuration:")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Reload: {reload}")
    print(f"   Log Level: {log_level}")
    print()
    
    print("🌐 API Endpoints will be available at:")
    print(f"   📖 Documentation: http://{host}:{port}/docs")
    print(f"   🏥 Health Check: http://{host}:{port}/health")
    print(f"   🚀 Start Pipeline: POST http://{host}:{port}/api/v1/pipeline/start")
    print(f"   📊 Check Status: GET http://{host}:{port}/api/v1/pipeline/status/{{session_id}}")
    print()
    
    print("📝 Example usage:")
    print(f"""
    # Start pipeline processing
    curl -X POST "http://{host}:{port}/api/v1/pipeline/start" \\
         -H "Content-Type: application/json" \\
         -d '{{"plant_name": "Jhajjar Power Plant"}}'
    
    # Check status (replace SESSION_ID with actual session ID)
    curl "http://{host}:{port}/api/v1/pipeline/status/SESSION_ID"
    """)
    
    print("=" * 60)
    print("🎯 Starting server...")
    
    try:
        # Import the app first to check for issues
        print("📦 Importing FastAPI app...")
        sys.path.insert(0, str(src_dir))
        from agent.app import app
        print("✅ FastAPI app imported successfully")

        # Start the server
        print("🚀 Starting uvicorn server...")
        uvicorn.run(
            app,  # Use the imported app directly
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
