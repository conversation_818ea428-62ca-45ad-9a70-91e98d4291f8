# Power Plant Information Extraction API

A comprehensive FastAPI application that takes a power plant name as input and executes the complete pipeline including database operations, image extraction, JSON data extraction, and all other operations.

## 🚀 Quick Start

### Prerequisites

1. **Environment Variables** - Set these required variables:
```bash
export GEMINI_API_KEY="your_gemini_api_key"
export AWS_ACCESS_KEY_ID="your_aws_access_key"
export AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
export AWS_DEFAULT_REGION="ap-south-1"
```

2. **Dependencies** - Install required packages:
```bash
cd backend
pip install -r requirements.txt  # or pip install -e .
```

### Starting the API

```bash
cd backend
python start_api.py
```

The API will be available at:
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **API Base**: http://localhost:8000/api/v1/

## 📋 API Endpoints

### Core Pipeline Endpoints

#### 1. Start Pipeline Processing
```http
POST /api/v1/pipeline/start
Content-Type: application/json

{
  "plant_name": "Jhajjar Power Plant",
  "options": {
    "include_images": true,
    "extract_units": true,
    "priority": "normal",
    "max_research_loops": 3,
    "initial_search_query_count": 5
  },
  "callback_url": "https://your-webhook.com/callback"  // optional
}
```

**Response:**
```json
{
  "session_id": "plant-abc12345-**********",
  "status": "queued",
  "message": "Pipeline started for 'Jhajjar Power Plant'. Use session ID 'plant-abc12345-**********' to check status.",
  "estimated_duration": 480
}
```

#### 2. Check Pipeline Status
```http
GET /api/v1/pipeline/status/{session_id}
```

**Response:**
```json
{
  "session_id": "plant-abc12345-**********",
  "status": "running",
  "progress": {
    "current_phase": "units_processing",
    "phases_completed": ["initialization", "langgraph_execution"],
    "total_phases": 6,
    "current_phase_progress": 75.0,
    "units_processed": 2,
    "total_units": 3
  },
  "started_at": "2024-01-01T10:00:00",
  "updated_at": "2024-01-01T10:05:30",
  "estimated_duration": 480
}
```

#### 3. Get Pipeline Results
```http
GET /api/v1/pipeline/results/{session_id}
```

**Response:**
```json
{
  "session_id": "plant-abc12345-**********",
  "plant_name": "Jhajjar Power Plant",
  "status": "completed",
  "results": {
    "s3_urls": {
      "organization": "https://s3.amazonaws.com/bucket/India/ORG_IN_ABC123/ORG_IN_ABC123.json",
      "plant": "https://s3.amazonaws.com/bucket/India/ORG_IN_ABC123/PLANT_IN_DEF456/plant_sk.json",
      "units": {
        "1": "https://s3.amazonaws.com/bucket/India/ORG_IN_ABC123/PLANT_IN_DEF456/unit_1.json",
        "2": "https://s3.amazonaws.com/bucket/India/ORG_IN_ABC123/PLANT_IN_DEF456/unit_2.json"
      }
    },
    "database_records": {
      "plants_found": 1,
      "plant_records": [{"name": "Jhajjar Power Plant", "org_uid": "ORG_IN_ABC123"}]
    },
    "image_urls": [
      "https://s3.amazonaws.com/bucket/images/jhajjar_1.jpg",
      "https://s3.amazonaws.com/bucket/images/jhajjar_2.jpg"
    ],
    "processing_stats": {
      "total_units_processed": 2,
      "s3_files_created": 4,
      "images_extracted": 2,
      "processing_time_seconds": 420
    }
  },
  "completed_at": "2024-01-01T10:07:00"
}
```

#### 4. Cancel Pipeline
```http
DELETE /api/v1/pipeline/cancel/{session_id}
```

### Batch Processing

#### Start Batch Processing
```http
POST /api/v1/pipeline/batch/start
Content-Type: application/json

{
  "plant_names": ["Jhajjar Power Plant", "Mundra Power Plant", "Adani Godda"],
  "options": {
    "include_images": true,
    "extract_units": true
  }
}
```

#### Check Batch Status
```http
GET /api/v1/pipeline/batch/status/{batch_id}
```

### Data Management

#### List Plants
```http
GET /api/v1/plants?page=1&page_size=50
```

#### Get Plant Details
```http
GET /api/v1/plants/{plant_id}
```

### System Endpoints

#### Health Check
```http
GET /health
```

#### System Metrics
```http
GET /api/v1/metrics
```

#### List Sessions
```http
GET /api/v1/sessions?status_filter=completed&limit=50
```

## 🔧 Configuration Options

### Pipeline Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `include_images` | boolean | `true` | Extract and upload plant images |
| `extract_units` | boolean | `true` | Extract unit-level data |
| `priority` | string | `"normal"` | Processing priority (low, normal, high) |
| `max_research_loops` | integer | `3` | Maximum research loops per phase |
| `initial_search_query_count` | integer | `5` | Initial search query count |

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `GEMINI_API_KEY` | Yes | Google Gemini API key |
| `AWS_ACCESS_KEY_ID` | Yes | AWS access key for S3 storage |
| `AWS_SECRET_ACCESS_KEY` | Yes | AWS secret key for S3 storage |
| `AWS_DEFAULT_REGION` | No | AWS region (default: ap-south-1) |
| `API_HOST` | No | API host (default: 0.0.0.0) |
| `API_PORT` | No | API port (default: 8000) |
| `API_LOG_LEVEL` | No | Log level (default: info) |

## 📊 Pipeline Phases

The pipeline executes in 6 phases:

1. **Initialization** - Session setup and state preparation
2. **LangGraph Execution** - Main research workflow (Organization → Plant → Units)
3. **Results Processing** - Extract and organize results
4. **Image Extraction** - Download and upload plant images (if enabled)
5. **Database Operations** - Verify data storage
6. **Finalization** - Prepare final results

## 🧪 Testing

### Test Database Connection
```http
GET /api/v1/debug/test-database
```

### Test Pipeline Components
```http
POST /api/v1/debug/test-pipeline?plant_name=Test Plant
```

## 📝 Example Usage

### Python Client Example

```python
import requests
import time

# Start pipeline
response = requests.post("http://localhost:8000/api/v1/pipeline/start", json={
    "plant_name": "Jhajjar Power Plant",
    "options": {
        "include_images": True,
        "extract_units": True
    }
})

session_id = response.json()["session_id"]
print(f"Started pipeline with session ID: {session_id}")

# Poll for completion
while True:
    status_response = requests.get(f"http://localhost:8000/api/v1/pipeline/status/{session_id}")
    status_data = status_response.json()
    
    print(f"Status: {status_data['status']} - Phase: {status_data['progress']['current_phase']}")
    
    if status_data["status"] in ["completed", "failed"]:
        break
    
    time.sleep(10)

# Get results
if status_data["status"] == "completed":
    results_response = requests.get(f"http://localhost:8000/api/v1/pipeline/results/{session_id}")
    results = results_response.json()
    print("Pipeline completed successfully!")
    print(f"S3 URLs: {results['results']['s3_urls']}")
```

### cURL Examples

```bash
# Start pipeline
curl -X POST "http://localhost:8000/api/v1/pipeline/start" \
  -H "Content-Type: application/json" \
  -d '{"plant_name": "Jhajjar Power Plant"}'

# Check status
curl "http://localhost:8000/api/v1/pipeline/status/plant-abc12345-**********"

# Get results
curl "http://localhost:8000/api/v1/pipeline/results/plant-abc12345-**********"
```

## 🚨 Error Handling

The API provides comprehensive error handling with structured error responses:

```json
{
  "error_code": "PIPELINE_EXECUTION_ERROR",
  "error_message": "Failed to extract unit data",
  "error_details": {
    "plant_name": "Jhajjar Power Plant",
    "session_id": "plant-abc12345-**********"
  },
  "timestamp": "2024-01-01T10:05:30Z"
}
```

## 📈 Monitoring

- **Logs**: Check `pipeline.log` for detailed execution logs
- **Metrics**: Use `/api/v1/metrics` for system statistics
- **Health**: Use `/health` for service health status
- **Sessions**: Use `/api/v1/sessions` to monitor active sessions

## 🔒 Security Notes

- Configure CORS appropriately for production
- Set proper `allowed_hosts` in TrustedHostMiddleware
- Use environment variables for sensitive configuration
- Consider adding API key authentication for production use
