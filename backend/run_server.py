#!/usr/bin/env python3
"""
Direct server runner that bypasses complex startup issues
"""

import sys
import os
from pathlib import Path

# Add src to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

print("🚀 Starting Power Plant Information Extraction API...")
print("=" * 60)

# Check environment variables
required_env_vars = [
    "GEMINI_API_KEY",
    "AWS_ACCESS_KEY_ID", 
    "AWS_SECRET_ACCESS_KEY"
]

missing_vars = []
for var in required_env_vars:
    if not os.getenv(var):
        missing_vars.append(var)

if missing_vars:
    print("❌ Missing required environment variables:")
    for var in missing_vars:
        print(f"   - {var}")
    print("\nPlease set these variables before starting the API.")
    sys.exit(1)

print("✅ Environment variables check passed")

try:
    print("📦 Importing FastAPI components...")
    
    # Import the app
    from agent.app import app
    print("✅ FastAPI app imported successfully")
    
    # Initialize database manually (bypass lifespan)
    print("💾 Initializing database...")
    from agent.database_manager import get_database_manager
    db_manager = get_database_manager()
    db_manager.create_tables()
    if db_manager.test_connection():
        print("✅ Database connection established")
    else:
        print("❌ Database connection failed")
    
    print("🌐 API will be available at:")
    print(f"   📖 Documentation: http://localhost:8000/docs")
    print(f"   🏥 Health Check: http://localhost:8000/health")
    print(f"   🚀 Start Pipeline: POST http://localhost:8000/api/v1/pipeline/start")
    print()
    
    print("📝 Example usage:")
    print(f"""
    # Start pipeline processing
    curl -X POST "http://localhost:8000/api/v1/pipeline/start" \\
         -H "Content-Type: application/json" \\
         -d '{{"plant_name": "Jhajjar Power Plant"}}'
    
    # Check status (replace SESSION_ID with actual session ID)
    curl "http://localhost:8000/api/v1/pipeline/status/SESSION_ID"
    """)
    
    print("=" * 60)
    print("🎯 Starting server...")
    
    # Start the server
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )
    
except KeyboardInterrupt:
    print("\n🛑 Server stopped by user")
except Exception as e:
    print(f"❌ Server failed to start: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
