#!/usr/bin/env python3
"""
Simple server to test the FastAPI without complex startup
"""

import sys
import os
from pathlib import Path

# Add src to path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

print("🚀 Starting simple FastAPI server...")

try:
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    import uvicorn
    
    # Create a simple app first
    app = FastAPI(title="Power Plant API - Simple Test")
    
    @app.get("/")
    async def root():
        return {"message": "Simple server is working!"}
    
    @app.get("/health")
    async def health():
        return {"status": "healthy", "message": "Simple server health check"}
    
    @app.post("/test")
    async def test_endpoint():
        return {"message": "Test endpoint working"}
    
    print("✅ Simple FastAPI app created")
    print("🌐 Starting server on http://localhost:8000")
    
    # Start the server
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info",workers = 6)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
