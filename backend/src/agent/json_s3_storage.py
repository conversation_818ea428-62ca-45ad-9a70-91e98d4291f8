"""
JSON S3 Storage Module for Power Plant Research Data

This module handles uploading JSON data at different processing levels
(organization, plant, unit) to S3 with proper folder structure.
"""

import os
import json
import re
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# S3 Configuration - Use S3-specific credentials
S3_AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-tech'

print(f"🔧 S3 Configuration:")
print(f"   - S3 Access Key: {S3_AWS_ACCESS_KEY[:8] + '...' if S3_AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")

def sanitize_plant_name(plant_name: str) -> str:
    """
    Convert power plant name to S3-safe folder name.
    
    Examples:
        "Jhajjar Power Plant" → "Jhajjar_Power_Plant"
        "NTPC Dadri (Stage-II)" → "NTPC_Dadri_Stage_II"
        "Adani Mundra Power Station" → "Adani_Mundra_Power_Station"
    """
    if not plant_name:
        return "Unknown_Plant"
    
    # Remove special characters except spaces and hyphens, replace & with _and_
    cleaned = plant_name.replace('&', '_and_')
    cleaned = re.sub(r'[^\w\s-]', '', cleaned)
    
    # Replace spaces and hyphens with underscores
    sanitized = cleaned.replace(' ', '_').replace('-', '_')
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    return sanitized if sanitized else "Unknown_Plant"

def get_country_folder_name(country: str) -> str:
    """Convert country name to S3-safe folder name"""
    if not country or country.lower() in ['unknown', 'not available', '']:
        return "Unknown_Country"

    # Replace spaces with underscores and remove special characters
    sanitized = re.sub(r'[^\w\s-]', '', country)
    sanitized = sanitized.replace(' ', '_').replace('-', '_')
    sanitized = re.sub(r'_+', '_', sanitized).strip('_')

    return sanitized if sanitized else "Unknown_Country"

def upload_hierarchical_json_to_s3(
    json_data: Dict[Any, Any],
    country: str,
    org_uid: str,
    plant_uid: str,
    filename: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 with hierarchical folder structure.

    Structure: Country/OrgUID/PlantUID/filename.json

    Args:
        json_data: Dictionary containing the data to upload
        country: Country name for top-level folder
        org_uid: Organization UID for second-level folder
        plant_uid: Plant UID for third-level folder (empty for org-level files)
        filename: Name of the JSON file
        session_id: Session ID for logging

    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "country": country,
                "org_uid": org_uid,
                "plant_uid": plant_uid,
                "file_type": filename.replace('.json', ''),
                "bucket": S3_BUCKET
            }
        }

        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)

        # Create hierarchical S3 key path
        country_folder = get_country_folder_name(country)

        if plant_uid:
            # Plant/Unit/Transition level: Country/OrgUID/PlantUID/filename.json
            s3_key = f"{country_folder}/{org_uid}/{plant_uid}/{filename}"
        else:
            # Organization level: Country/OrgUID/OrgUID.json
            s3_key = f"{country_folder}/{org_uid}/{filename}"

        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )

        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"

        print(f"[Session {session_id}] ✅ Hierarchical JSON uploaded: {s3_url}")
        print(f"[Session {session_id}] 📁 S3 Path: {s3_key}")
        return s3_url

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload hierarchical {filename}: {str(e)}")
        return None

def store_organization_data(
    org_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store organization-level data to S3.
    
    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to organization data as primary key
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")
    
    if org_uid:
        org_data["org_uid"] = org_uid
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = org_data.get("pk", "NOT_FOUND")
        org_data["pk"] = org_uid
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
        print(f"[Session {session_id}] 🔑 Added UID to organization data: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_organization_data")
        print(f"[Session {session_id}] 🔍 EMERGENCY FIX: Attempting to generate UID from org data...")

        # EMERGENCY FIX: Try to generate UID from organization data if not provided
        org_name = org_data.get("organization_name", "")
        country = org_data.get("country_name", "")

        if org_name and country:
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                emergency_uid = db_manager.generate_org_uid(org_name, country)

                org_data["org_uid"] = emergency_uid
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = emergency_uid
                print(f"[Session {session_id}] 🚨 EMERGENCY UID GENERATED: '{emergency_uid}'")
                print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{emergency_uid}'")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Emergency UID generation failed: {e}")
                # Last resort: set pk to null instead of "default null"
                org_data["pk"] = None
                print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
        else:
            print(f"[Session {session_id}] ❌ Cannot generate emergency UID: missing org_name or country")
            # Last resort: set pk to null instead of "default null"
            org_data["pk"] = None
            print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
    
    # CRITICAL FIX: Enforce fixed values for off_peak_hours and peak_hours right before storage
    # This ensures they are NEVER null regardless of what the LLM generated
    org_data["off_peak_hours"] = 0.466
    org_data["peak_hours"] = 0.9
    print(f"[Session {session_id}] 🔧 FINAL ENFORCEMENT: off_peak_hours=0.466, peak_hours=0.9")

    # Get country from organization data or database
    country = org_data.get("country_name", "Unknown")
    if country in ["Unknown", "Not available", "", None]:
        # Try to get country from database using plant name
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            plant_info = db_manager.check_plant_exists(plant_name)
            if plant_info and plant_info.get("country"):
                country = plant_info["country"]
                print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")
            country = "Unknown"

    # Get org_uid for filename and folder structure
    final_org_uid = org_data.get("org_uid", "UNKNOWN_ORG")
    filename = f"{final_org_uid}.json"

    print(f"[Session {session_id}] 🏢 Storing organization data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {final_org_uid}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(org_data, country, final_org_uid, "", filename, session_id)

def store_plant_data(
    plant_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None,
    plant_uid: str = None
) -> Optional[str]:
    """
    Store plant-level data to S3.

    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID
        plant_uid: Plant UID (primary key for plant level)

    Returns:
        S3 URL of uploaded file
    """
    # Add UIDs to plant data - pk should be plant_uid, not org_uid
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_uid = '{plant_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_data keys = {list(plant_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{plant_data.get('pk', 'NOT_FOUND')}'")

    # Set org_uid for reference
    if org_uid:
        plant_data["org_uid"] = org_uid
        print(f"[Session {session_id}] 🔑 Added org_uid to plant data: {org_uid}")

    # Set pk to plant_uid (NEW: plant_uid as primary key instead of org_uid)
    if plant_uid:
        old_pk = plant_data.get("pk", "NOT_FOUND")
        plant_data["pk"] = plant_uid
        plant_data["plant_uid"] = plant_uid
        print(f"[Session {session_id}] ✅ Set pk field to plant_uid: '{old_pk}' → '{plant_uid}'")
        print(f"[Session {session_id}] 🔑 Added plant_uid to plant data: {plant_uid}")
    elif org_uid:
        # Fallback to org_uid if plant_uid not available
        old_pk = plant_data.get("pk", "NOT_FOUND")
        plant_data["pk"] = org_uid
        print(f"[Session {session_id}] ⚠️ No plant_uid, using org_uid as pk: '{old_pk}' → '{org_uid}'")
    else:
        print(f"[Session {session_id}] ❌ No plant_uid or org_uid provided to store_plant_data")
    
    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    # Generate filename from SK value
    sk_value = plant_data.get("sk", "plant_unknown")
    filename = f"{sk_value}.json"

    # Ensure org_uid is not None or empty
    if not org_uid:  # Handles both None and empty string
        org_uid = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_uid was None/empty, using fallback: {org_uid}")

    if not plant_uid:  # Handles both None and empty string
        plant_uid = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_uid was None/empty, using fallback: {plant_uid}")

    print(f"[Session {session_id}] 🏭 Storing plant data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_uid}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_uid}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(plant_data, country, org_uid, plant_uid, filename, session_id)

def store_unit_data(
    unit_data: Dict[Any, Any],
    plant_name: str,
    unit_number: str,
    session_id: str = "unknown",
    org_uid: str = None,
    plant_uid: str = None
) -> Optional[str]:
    """
    Store individual unit data to S3.

    Args:
        unit_data: Unit data dictionary
        plant_name: Original plant name from user query
        unit_number: Unit number (e.g., "1", "2", "3")
        session_id: Session ID for tracking
        org_uid: Organization UID
        plant_uid: Plant UID (primary key for unit level)

    Returns:
        S3 URL of uploaded file
    """
    # Add UIDs to unit data - pk should be plant_uid, not org_uid
    print(f"[Session {session_id}] 🔍 DEBUG Unit {unit_number}: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG Unit {unit_number}: plant_uid = '{plant_uid}'")

    # Set org_uid for reference
    if org_uid:
        unit_data["org_uid"] = org_uid
        print(f"[Session {session_id}] 🔑 Added org_uid to Unit {unit_number} data: {org_uid}")

    # Set pk to plant_uid (NEW: plant_uid as primary key instead of org_uid)
    if plant_uid:
        old_pk = unit_data.get("pk", "NOT_FOUND")
        unit_data["pk"] = plant_uid
        unit_data["plant_uid"] = plant_uid
        print(f"[Session {session_id}] ✅ Set pk field to plant_uid: '{old_pk}' → '{plant_uid}'")
        print(f"[Session {session_id}] 🔑 Added plant_uid to Unit {unit_number} data: {plant_uid}")
    elif org_uid:
        # Fallback to org_uid if plant_uid not available
        old_pk = unit_data.get("pk", "NOT_FOUND")
        unit_data["pk"] = org_uid
        print(f"[Session {session_id}] ⚠️ No plant_uid, using org_uid as pk: '{old_pk}' → '{org_uid}'")
    else:
        print(f"[Session {session_id}] ❌ No plant_uid or org_uid provided to store_unit_data")
    
    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    # Generate filename from SK value
    sk_value = unit_data.get("sk", f"unit_{unit_number}_unknown")
    filename = f"unit_{unit_number}_{sk_value}.json"

    # Ensure org_uid and plant_uid are not None or empty
    if not org_uid:  # Handles both None and empty string
        org_uid = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_uid was None/empty, using fallback: {org_uid}")

    if not plant_uid:  # Handles both None and empty string
        plant_uid = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_uid was None/empty, using fallback: {plant_uid}")

    print(f"[Session {session_id}] ⚡ Storing Unit {unit_number} data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_uid}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_uid}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(unit_data, country, org_uid, plant_uid, filename, session_id)

def store_transition_plan_data(
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None,
    plant_uid: str = None
) -> Optional[str]:
    """
    Store Level-4 transition plan data to S3.

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (for reference)
        plant_uid: Plant UID (primary key)

    Returns:
        S3 URL of uploaded file
    """
    # CRITICAL FIX: Use plant_uid as pk, not org_uid
    pk_value = plant_uid if plant_uid else org_uid if org_uid else ""

    # Create Level-4 transition plan JSON structure
    transition_plan_data = {
        "pk": pk_value,
        "sk": "transition_plan",
        "selected_plan_id": "",
        "transitionPlanStratName": "",
        "plant_uid": plant_uid if plant_uid else "",
        "org_uid": org_uid if org_uid else ""
    }

    print(f"[Session {session_id}] 🔍 DEBUG: Creating Level-4 transition plan")
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}', plant_uid = '{plant_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: pk_value = '{pk_value}'")
    print(f"[Session {session_id}] 🔍 DEBUG: transition_plan_data = {transition_plan_data}")

    if plant_uid:
        print(f"[Session {session_id}] 🔑 Added plant_uid as pk to transition plan: {plant_uid}")
    elif org_uid:
        print(f"[Session {session_id}] ⚠️ Using org_uid as fallback pk: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No plant_uid or org_uid provided to store_transition_plan_data")

    # Get country from database
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")

    filename = "transition_plan.json"

    # Ensure org_uid and plant_uid are not None or empty
    if not org_uid:  # Handles both None and empty string
        org_uid = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_uid was None/empty, using fallback: {org_uid}")

    if not plant_uid:  # Handles both None and empty string
        plant_uid = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_uid was None/empty, using fallback: {plant_uid}")

    print(f"[Session {session_id}] 📋 Storing Level-4 transition plan hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_uid}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_uid}")
    print(f"[Session {session_id}] 📄 Filename: {filename}")

    return upload_hierarchical_json_to_s3(transition_plan_data, country, org_uid, plant_uid, filename, session_id)

def get_plant_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate hierarchical S3 URLs for all expected files of a plant (for state tracking).

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        Dictionary with hierarchical URL structure for state management
    """
    # Get plant metadata from database for hierarchical structure
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)

        if plant_info:
            country = get_country_folder_name(plant_info.get("country", "Unknown"))
            org_uid = plant_info.get("org_uid", "UNKNOWN_ORG")
            plant_uid = plant_info.get("plant_uid", "UNKNOWN_PLANT")

            base_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{country}/{org_uid}"
            plant_base_url = f"{base_url}/{plant_uid}"

            return {
                "plant_name": plant_name,
                "country": country,
                "org_uid": org_uid,
                "plant_uid": plant_uid,
                "base_url": base_url,
                "plant_base_url": plant_base_url,
                "organization": f"{base_url}/{org_uid}.json",
                "plant": f"{plant_base_url}/plant_sk.json",  # Will be updated with actual SK
                "units": {},  # Will be populated as units are processed
                "transition_plan": f"{plant_base_url}/transition_plan.json"
            }
        else:
            print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}")
            return {"error": "Plant not found in database"}

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error generating hierarchical S3 URLs: {e}")
        return {"error": str(e)}

def check_s3_connection(session_id: str = "test") -> bool:
    """
    Test S3 connection and credentials.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        
        # Try to list objects (this will fail if credentials are wrong)
        s3_client.head_bucket(Bucket=S3_BUCKET)
        print(f"[Session {session_id}] ✅ S3 connection successful to bucket: {S3_BUCKET}")
        return True
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ S3 connection failed: {str(e)}")
        return False

# For testing/debugging
if __name__ == "__main__":
    # Test sanitization
    test_names = [
        "Jhajjar Power Plant",
        "NTPC Dadri (Stage-II)",
        "Adani Mundra Power Station",
        "Tata Power Plant - Unit 1&2"
    ]
    
    print("Testing plant name sanitization:")
    for name in test_names:
        sanitized = sanitize_plant_name(name)
        print(f"'{name}' → '{sanitized}'")
    
    # Test S3 connection
    print("\nTesting S3 connection:")
    check_s3_connection()