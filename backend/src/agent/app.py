# mypy: disable - error - code = "no-untyped-def,misc"
import asyncio
import uuid
import time
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, status, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import BaseModel, Field, validator
from langchain_core.messages import HumanMessage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pipeline.log')
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import existing components
from agent.graph import graph
from agent.database_manager import get_database_manager
from agent.json_s3_storage import get_plant_s3_urls
from agent.image_extraction import extract_and_upload_images

# Global session storage for tracking pipeline executions
pipeline_sessions: Dict[str, Dict[str, Any]] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("🚀 Starting Power Plant Extraction API...")

    # Initialize database
    try:
        db_manager = get_database_manager()
        db_manager.create_tables()
        if db_manager.test_connection():
            logger.info("✅ Database connection established")
        else:
            logger.error("❌ Database connection failed")
    except Exception as e:
        logger.error(f"⚠️ Database initialization error: {e}")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Power Plant Extraction API...")

# Define the FastAPI app (temporarily disable lifespan for debugging)
app = FastAPI(
    title="Power Plant Information Extraction API",
    description="A comprehensive API for extracting structured information about power plants using LangGraph and Google Gemini models. Includes database operations, image extraction, JSON data extraction, and complete pipeline processing.",
    version="2.0.0"
    # lifespan=lifespan  # Temporarily disabled for debugging
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware for security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {exc}")
    logger.error(f"Traceback: {traceback.format_exc()}")

    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred. Please check the logs.",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests"""
    start_time = time.time()

    # Log request
    logger.info(f"📥 {request.method} {request.url}")

    # Process request
    response = await call_next(request)

    # Log response
    process_time = time.time() - start_time
    logger.info(f"📤 {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")

    return response

# ============================================================================
# PYDANTIC MODELS FOR REQUEST/RESPONSE VALIDATION
# ============================================================================

class PipelineOptions(BaseModel):
    """Options for pipeline processing"""
    include_images: bool = Field(default=True, description="Whether to extract and upload images")
    extract_units: bool = Field(default=True, description="Whether to extract unit-level data")
    priority: str = Field(default="normal", description="Processing priority (low, normal, high)")
    max_research_loops: int = Field(default=3, description="Maximum research loops per phase")
    initial_search_query_count: int = Field(default=5, description="Initial search query count")

    @validator('priority')
    def validate_priority(cls, v):
        if v not in ['low', 'normal', 'high']:
            raise ValueError('Priority must be one of: low, normal, high')
        return v

class PipelineStartRequest(BaseModel):
    """Request model for starting pipeline processing"""
    plant_name: str = Field(..., min_length=1, max_length=200, description="Name of the power plant to process")
    options: Optional[PipelineOptions] = Field(default_factory=PipelineOptions, description="Processing options")
    callback_url: Optional[str] = Field(default=None, description="Optional webhook URL for completion notification")

    @validator('plant_name')
    def validate_plant_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Plant name cannot be empty')
        return v.strip()

class ProgressInfo(BaseModel):
    """Progress information for pipeline execution"""
    current_phase: str = Field(..., description="Current processing phase")
    phases_completed: List[str] = Field(default_factory=list, description="List of completed phases")
    total_phases: int = Field(default=6, description="Total number of phases")
    current_phase_progress: float = Field(default=0.0, description="Progress within current phase (0-100)")
    estimated_completion_time: Optional[str] = Field(default=None, description="Estimated completion time")
    units_processed: int = Field(default=0, description="Number of units processed")
    total_units: int = Field(default=0, description="Total number of units to process")

class ResultsInfo(BaseModel):
    """Results information from pipeline execution"""
    s3_urls: Dict[str, Any] = Field(default_factory=dict, description="S3 URLs for stored data")
    database_records: Dict[str, Any] = Field(default_factory=dict, description="Database record information")
    image_urls: List[str] = Field(default_factory=list, description="Uploaded image URLs")
    processing_stats: Dict[str, Any] = Field(default_factory=dict, description="Processing statistics")

class ErrorInfo(BaseModel):
    """Error information"""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat(), description="Error timestamp")

class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status"""
    session_id: str = Field(..., description="Unique session identifier")
    status: str = Field(..., description="Current status (queued, running, completed, failed, cancelled)")
    progress: ProgressInfo = Field(..., description="Progress information")
    results: Optional[ResultsInfo] = Field(default=None, description="Results (available when completed)")
    error: Optional[ErrorInfo] = Field(default=None, description="Error information (if failed)")
    started_at: str = Field(..., description="Start timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    estimated_duration: Optional[int] = Field(default=None, description="Estimated duration in seconds")

class PipelineStartResponse(BaseModel):
    """Response model for pipeline start"""
    session_id: str = Field(..., description="Unique session identifier")
    status: str = Field(default="queued", description="Initial status")
    message: str = Field(..., description="Success message")
    estimated_duration: Optional[int] = Field(default=None, description="Estimated duration in seconds")

class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(default="healthy", description="Service health status")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat(), description="Health check timestamp")
    version: str = Field(default="2.0.0", description="API version")
    database_status: str = Field(..., description="Database connection status")
    active_sessions: int = Field(..., description="Number of active pipeline sessions")

class PlantListResponse(BaseModel):
    """Response for listing plants"""
    plants: List[Dict[str, Any]] = Field(..., description="List of plants in database")
    total_count: int = Field(..., description="Total number of plants")
    page: int = Field(default=1, description="Current page number")
    page_size: int = Field(default=50, description="Page size")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def generate_session_id() -> str:
    """Generate a unique session ID"""
    return f"plant-{uuid.uuid4().hex[:8]}-{int(time.time())}"

def get_session_info(session_id: str) -> Optional[Dict[str, Any]]:
    """Get session information"""
    return pipeline_sessions.get(session_id)

def update_session_status(session_id: str, status: str, **kwargs):
    """Update session status and information"""
    if session_id in pipeline_sessions:
        pipeline_sessions[session_id].update({
            'status': status,
            'updated_at': datetime.utcnow().isoformat(),
            **kwargs
        })

def estimate_processing_duration(plant_name: str, options: PipelineOptions) -> int:
    """Estimate processing duration in seconds based on options"""
    base_duration = 300  # 5 minutes base

    # Add time for image extraction
    if options.include_images:
        base_duration += 120  # 2 minutes for images

    # Add time for unit extraction
    if options.extract_units:
        base_duration += 180  # 3 minutes for units

    # Adjust for research loops
    base_duration += (options.max_research_loops - 3) * 60

    return base_duration

# ============================================================================
# PIPELINE EXECUTION SERVICE
# ============================================================================

async def execute_pipeline(session_id: str, plant_name: str, options: PipelineOptions):
    """Execute the complete pipeline for a power plant"""
    try:
        logger.info(f"🚀 [Session {session_id}] Starting pipeline execution for: {plant_name}")

        # Update status to running
        update_session_status(session_id, "running",
                            progress=ProgressInfo(
                                current_phase="initialization",
                                phases_completed=[],
                                current_phase_progress=0.0
                            ))

        # Phase 1: Initialize session and prepare state
        logger.info(f"📋 [Session {session_id}] Phase 1: Initializing session...")
        update_session_status(session_id, "running",
                            progress=ProgressInfo(
                                current_phase="initialization",
                                phases_completed=[],
                                current_phase_progress=25.0
                            ))

        # Create initial state for LangGraph
        initial_state = {
            "messages": [HumanMessage(content=plant_name)],
            "session_id": session_id,
            "initial_search_query_count": options.initial_search_query_count,
            "max_research_loops": options.max_research_loops,
            "reasoning_model": "gemini-2.0-flash"
        }

        # Phase 2: Execute LangGraph pipeline
        logger.info(f"🔄 [Session {session_id}] Phase 2: Executing LangGraph pipeline...")
        update_session_status(session_id, "running",
                            progress=ProgressInfo(
                                current_phase="langgraph_execution",
                                phases_completed=["initialization"],
                                current_phase_progress=0.0
                            ))

        # Execute the graph
        final_state = None
        async for state in graph.astream(initial_state):
            # Update progress based on current state
            current_phase = "langgraph_execution"
            progress = 50.0  # Approximate progress

            if "org_level_complete" in state and state.get("org_level_complete"):
                progress = 70.0
                current_phase = "organization_complete"

            if "unit_results" in state and state.get("unit_results"):
                progress = 90.0
                current_phase = "units_processing"

            update_session_status(session_id, "running",
                                progress=ProgressInfo(
                                    current_phase=current_phase,
                                    phases_completed=["initialization"],
                                    current_phase_progress=progress
                                ))

            final_state = state

        # Phase 3: Process results and extract data
        logger.info(f"📊 [Session {session_id}] Phase 3: Processing results...")
        update_session_status(session_id, "running",
                            progress=ProgressInfo(
                                current_phase="results_processing",
                                phases_completed=["initialization", "langgraph_execution"],
                                current_phase_progress=0.0
                            ))

        # Extract S3 URLs and other results from final state
        s3_urls = final_state.get("s3_json_urls", {}) if final_state else {}
        unit_results = final_state.get("unit_results", []) if final_state else []

        # Phase 4: Image extraction (if enabled)
        image_urls = []
        if options.include_images:
            logger.info(f"🖼️ [Session {session_id}] Phase 4: Extracting images...")
            update_session_status(session_id, "running",
                                progress=ProgressInfo(
                                    current_phase="image_extraction",
                                    phases_completed=["initialization", "langgraph_execution", "results_processing"],
                                    current_phase_progress=0.0
                                ))

            try:
                image_urls = extract_and_upload_images(plant_name, session_id)
                logger.info(f"✅ [Session {session_id}] Extracted {len(image_urls)} images")
            except Exception as e:
                logger.warning(f"⚠️ [Session {session_id}] Image extraction failed: {e}")

        # Phase 5: Database operations
        logger.info(f"💾 [Session {session_id}] Phase 5: Database operations...")
        update_session_status(session_id, "running",
                            progress=ProgressInfo(
                                current_phase="database_operations",
                                phases_completed=["initialization", "langgraph_execution", "results_processing", "image_extraction"],
                                current_phase_progress=0.0
                            ))

        # Database operations are handled within the LangGraph pipeline
        # Just verify the data was stored
        db_manager = get_database_manager()
        database_records = {}

        try:
            # Check if plant was stored in database
            plant_record = db_manager.check_plant_exists(plant_name)
            if plant_record:
                database_records = {
                    "plants_found": 1,
                    "plant_records": [{
                        "name": plant_record["plant_name"],
                        "org_uid": plant_record["org_uid"],
                        "plant_uid": plant_record.get("plant_uid", "")
                    }]
                }
        except Exception as e:
            logger.warning(f"⚠️ [Session {session_id}] Database check failed: {e}")

        # Phase 6: Finalization
        logger.info(f"✅ [Session {session_id}] Phase 6: Finalizing...")

        # Prepare final results
        results = ResultsInfo(
            s3_urls=s3_urls,
            database_records=database_records,
            image_urls=image_urls,
            processing_stats={
                "total_units_processed": len(unit_results),
                "s3_files_created": len(s3_urls),
                "images_extracted": len(image_urls),
                "processing_time_seconds": int(time.time() - pipeline_sessions[session_id]["started_timestamp"])
            }
        )

        # Update final status
        update_session_status(session_id, "completed",
                            progress=ProgressInfo(
                                current_phase="completed",
                                phases_completed=["initialization", "langgraph_execution", "results_processing", "image_extraction", "database_operations", "finalization"],
                                current_phase_progress=100.0,
                                units_processed=len(unit_results),
                                total_units=len(unit_results)
                            ),
                            results=results)

        logger.info(f"🎉 [Session {session_id}] Pipeline execution completed successfully!")

    except Exception as e:
        logger.error(f"❌ [Session {session_id}] Pipeline execution failed: {e}")
        logger.error(f"❌ [Session {session_id}] Traceback: {traceback.format_exc()}")

        # Update status to failed
        error_info = ErrorInfo(
            error_code="PIPELINE_EXECUTION_ERROR",
            error_message=str(e),
            error_details={"plant_name": plant_name, "session_id": session_id}
        )

        update_session_status(session_id, "failed", error=error_info)

        # Don't re-raise to prevent background task from crashing
        # The error is already captured in the session status

# ============================================================================
# API ENDPOINTS
# ============================================================================

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Power Plant Information Extraction API",
        "version": "2.0.0",
        "documentation": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db_manager = get_database_manager()
        db_status = "healthy" if db_manager.test_connection() else "unhealthy"

        return HealthResponse(
            status="healthy" if db_status == "healthy" else "degraded",
            database_status=db_status,
            active_sessions=len([s for s in pipeline_sessions.values() if s.get("status") == "running"])
        )
    except Exception as e:
        return HealthResponse(
            status="unhealthy",
            database_status="error",
            active_sessions=0
        )

@app.post("/api/v1/pipeline/start", response_model=PipelineStartResponse)
async def start_pipeline(request: PipelineStartRequest, background_tasks: BackgroundTasks):
    """Start pipeline processing for a power plant"""
    try:
        # Generate session ID
        session_id = generate_session_id()

        # Estimate processing duration
        estimated_duration = estimate_processing_duration(request.plant_name, request.options)

        # Initialize session tracking
        pipeline_sessions[session_id] = {
            "session_id": session_id,
            "plant_name": request.plant_name,
            "options": request.options.dict(),
            "status": "queued",
            "started_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "started_timestamp": time.time(),
            "estimated_duration": estimated_duration,
            "callback_url": request.callback_url,
            "progress": ProgressInfo(
                current_phase="queued",
                phases_completed=[],
                current_phase_progress=0.0
            ).dict()
        }

        # Add pipeline execution to background tasks
        background_tasks.add_task(execute_pipeline, session_id, request.plant_name, request.options)

        logger.info(f"🚀 Started pipeline for '{request.plant_name}' with session ID: {session_id}")

        return PipelineStartResponse(
            session_id=session_id,
            status="queued",
            message=f"Pipeline started for '{request.plant_name}'. Use session ID '{session_id}' to check status.",
            estimated_duration=estimated_duration
        )

    except Exception as e:
        logger.error(f"❌ Error starting pipeline: {e}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start pipeline: {str(e)}"
        )

@app.get("/api/v1/pipeline/status/{session_id}", response_model=PipelineStatusResponse)
async def get_pipeline_status(session_id: str):
    """Get pipeline processing status"""
    session_info = get_session_info(session_id)

    if not session_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session '{session_id}' not found"
        )

    # Convert progress dict back to ProgressInfo model
    progress_data = session_info.get("progress", {})
    if isinstance(progress_data, dict) and progress_data:
        progress = ProgressInfo(**progress_data)
    elif isinstance(progress_data, ProgressInfo):
        progress = progress_data
    else:
        progress = ProgressInfo(current_phase="unknown")

    # Convert results dict back to ResultsInfo model if available
    results = None
    results_data = session_info.get("results")
    if results_data:
        if isinstance(results_data, dict):
            results = ResultsInfo(**results_data)
        elif isinstance(results_data, ResultsInfo):
            results = results_data

    # Convert error dict back to ErrorInfo model if available
    error = None
    error_data = session_info.get("error")
    if error_data:
        if isinstance(error_data, dict):
            error = ErrorInfo(**error_data)
        elif isinstance(error_data, ErrorInfo):
            error = error_data

    return PipelineStatusResponse(
        session_id=session_id,
        status=session_info["status"],
        progress=progress,
        results=results,
        error=error,
        started_at=session_info["started_at"],
        updated_at=session_info["updated_at"],
        estimated_duration=session_info.get("estimated_duration")
    )

@app.get("/api/v1/pipeline/results/{session_id}")
async def get_pipeline_results(session_id: str):
    """Get detailed pipeline results"""
    session_info = get_session_info(session_id)

    if not session_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session '{session_id}' not found"
        )

    if session_info["status"] != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Pipeline not completed yet. Current status: {session_info['status']}"
        )

    results = session_info.get("results")
    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Results not available"
        )

    return {
        "session_id": session_id,
        "plant_name": session_info["plant_name"],
        "status": session_info["status"],
        "results": results,
        "completed_at": session_info["updated_at"]
    }

@app.delete("/api/v1/pipeline/cancel/{session_id}")
async def cancel_pipeline(session_id: str):
    """Cancel pipeline processing"""
    session_info = get_session_info(session_id)

    if not session_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session '{session_id}' not found"
        )

    if session_info["status"] in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel pipeline with status: {session_info['status']}"
        )

    # Update status to cancelled
    update_session_status(session_id, "cancelled")

    return {
        "session_id": session_id,
        "status": "cancelled",
        "message": "Pipeline processing cancelled"
    }

@app.get("/api/v1/sessions", response_model=Dict[str, Any])
async def list_sessions(status_filter: Optional[str] = None, limit: int = 50):
    """List pipeline sessions with optional status filtering"""
    sessions = list(pipeline_sessions.values())

    # Filter by status if provided
    if status_filter:
        sessions = [s for s in sessions if s.get("status") == status_filter]

    # Limit results
    sessions = sessions[:limit]

    return {
        "sessions": sessions,
        "total_count": len(sessions),
        "filtered_by_status": status_filter
    }

@app.get("/api/v1/plants", response_model=PlantListResponse)
async def list_plants(page: int = 1, page_size: int = 50):
    """List plants from database"""
    try:
        db_manager = get_database_manager()

        # Calculate offset
        offset = (page - 1) * page_size

        # Get plants from database
        session = db_manager.get_session()
        try:
            plants_query = session.query(db_manager.PlantRegistry).offset(offset).limit(page_size)
            plants = plants_query.all()

            total_count = session.query(db_manager.PlantRegistry).count()

            plants_data = []
            for plant in plants:
                plants_data.append({
                    "id": plant.id,
                    "plant_name": plant.plant_name,
                    "org_uid": plant.org_uid,
                    "country": plant.country,
                    "status": plant.status.value,
                    "created_at": plant.created_at.isoformat(),
                    "updated_at": plant.updated_at.isoformat()
                })

            return PlantListResponse(
                plants=plants_data,
                total_count=total_count,
                page=page,
                page_size=page_size
            )

        finally:
            session.close()

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve plants: {str(e)}"
        )

@app.get("/api/v1/plants/{plant_id}")
async def get_plant_details(plant_id: int):
    """Get detailed information about a specific plant"""
    try:
        db_manager = get_database_manager()
        session = db_manager.get_session()

        try:
            plant = session.query(db_manager.PlantRegistry).filter(
                db_manager.PlantRegistry.id == plant_id
            ).first()

            if not plant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Plant with ID {plant_id} not found"
                )

            return {
                "id": plant.id,
                "plant_name": plant.plant_name,
                "org_uid": plant.org_uid,
                "country": plant.country,
                "status": plant.status.value,
                "created_at": plant.created_at.isoformat(),
                "updated_at": plant.updated_at.isoformat(),
                "metadata": plant.metadata
            }

        finally:
            session.close()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve plant details: {str(e)}"
        )

class BatchPipelineRequest(BaseModel):
    """Request model for batch pipeline processing"""
    plant_names: List[str] = Field(..., description="List of plant names to process")
    options: Optional[PipelineOptions] = Field(default_factory=PipelineOptions, description="Processing options")

@app.post("/api/v1/pipeline/batch/start")
async def start_batch_pipeline(
    request: BatchPipelineRequest,
    background_tasks: BackgroundTasks
):
    """Start batch pipeline processing for multiple plants"""
    if not request.plant_names:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Plant names list cannot be empty"
        )

    if len(request.plant_names) > 10:  # Limit batch size
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Batch size cannot exceed 10 plants"
        )

    batch_id = f"batch-{uuid.uuid4().hex[:8]}-{int(time.time())}"
    session_ids = []

    try:
        for plant_name in request.plant_names:
            # Create individual session for each plant
            session_id = generate_session_id()
            session_ids.append(session_id)

            # Initialize session
            estimated_duration = estimate_processing_duration(plant_name, request.options)
            pipeline_sessions[session_id] = {
                "session_id": session_id,
                "batch_id": batch_id,
                "plant_name": plant_name,
                "options": request.options.dict(),
                "status": "queued",
                "started_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "started_timestamp": time.time(),
                "estimated_duration": estimated_duration,
                "progress": ProgressInfo(
                    current_phase="queued",
                    phases_completed=[],
                    current_phase_progress=0.0
                ).dict()
            }

            # Add to background tasks
            background_tasks.add_task(execute_pipeline, session_id, plant_name, request.options)

        logger.info(f"🚀 Started batch pipeline for {len(request.plant_names)} plants with batch ID: {batch_id}")

        return {
            "batch_id": batch_id,
            "session_ids": session_ids,
            "plant_names": request.plant_names,
            "status": "queued",
            "message": f"Batch pipeline started for {len(request.plant_names)} plants"
        }

    except Exception as e:
        logger.error(f"❌ Error starting batch pipeline: {e}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start batch pipeline: {str(e)}"
        )

@app.get("/api/v1/pipeline/batch/status/{batch_id}")
async def get_batch_status(batch_id: str):
    """Get status of batch pipeline processing"""
    batch_sessions = [s for s in pipeline_sessions.values() if s.get("batch_id") == batch_id]

    if not batch_sessions:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Batch '{batch_id}' not found"
        )

    # Calculate overall batch status
    statuses = [s["status"] for s in batch_sessions]

    if all(status == "completed" for status in statuses):
        overall_status = "completed"
    elif any(status == "failed" for status in statuses):
        overall_status = "partial_failure"
    elif any(status == "running" for status in statuses):
        overall_status = "running"
    else:
        overall_status = "queued"

    return {
        "batch_id": batch_id,
        "overall_status": overall_status,
        "total_plants": len(batch_sessions),
        "completed": len([s for s in batch_sessions if s["status"] == "completed"]),
        "running": len([s for s in batch_sessions if s["status"] == "running"]),
        "failed": len([s for s in batch_sessions if s["status"] == "failed"]),
        "queued": len([s for s in batch_sessions if s["status"] == "queued"]),
        "sessions": [
            {
                "session_id": s["session_id"],
                "plant_name": s["plant_name"],
                "status": s["status"],
                "updated_at": s["updated_at"]
            }
            for s in batch_sessions
        ]
    }

# ============================================================================
# TESTING AND DEBUG ENDPOINTS
# ============================================================================

@app.get("/api/v1/debug/test-database")
async def test_database_connection():
    """Test database connection and operations"""
    try:
        db_manager = get_database_manager()

        # Test connection
        connection_ok = db_manager.test_connection()

        # Test basic operations
        session = db_manager.get_session()
        try:
            # Count plants
            plant_count = session.query(db_manager.PlantRegistry).count()

            return {
                "database_connection": "ok" if connection_ok else "failed",
                "database_type": db_manager.db_type,
                "plant_count": plant_count,
                "timestamp": datetime.utcnow().isoformat()
            }
        finally:
            session.close()

    except Exception as e:
        return {
            "database_connection": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.post("/api/v1/debug/test-pipeline")
async def test_pipeline_components(plant_name: str = "Test Plant"):
    """Test individual pipeline components"""
    try:
        results = {}

        # Test 1: S3 URL generation
        try:
            s3_urls = get_plant_s3_urls(plant_name, "test-session")
            results["s3_url_generation"] = "ok"
            results["s3_urls_sample"] = s3_urls
        except Exception as e:
            results["s3_url_generation"] = f"error: {str(e)}"

        # Test 2: Database operations
        try:
            db_manager = get_database_manager()
            connection_ok = db_manager.test_connection()
            results["database_connection"] = "ok" if connection_ok else "failed"
        except Exception as e:
            results["database_connection"] = f"error: {str(e)}"

        # Test 3: Session management
        try:
            test_session_id = generate_session_id()
            results["session_generation"] = "ok"
            results["test_session_id"] = test_session_id
        except Exception as e:
            results["session_generation"] = f"error: {str(e)}"

        return {
            "test_results": results,
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "ok" if all("error" not in str(v) for v in results.values()) else "issues_found"
        }

    except Exception as e:
        return {
            "test_results": {"overall_error": str(e)},
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "failed"
        }

@app.get("/api/v1/metrics")
async def get_metrics():
    """Get system metrics and statistics"""
    try:
        # Session statistics
        total_sessions = len(pipeline_sessions)
        status_counts = {}
        for session in pipeline_sessions.values():
            status = session.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1

        # Database statistics
        db_stats = {}
        try:
            db_manager = get_database_manager()
            session = db_manager.get_session()
            try:
                db_stats["total_plants"] = session.query(db_manager.PlantRegistry).count()
                db_stats["connection_status"] = "healthy"
            finally:
                session.close()
        except Exception as e:
            db_stats["connection_status"] = "error"
            db_stats["error"] = str(e)

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "session_statistics": {
                "total_sessions": total_sessions,
                "status_breakdown": status_counts
            },
            "database_statistics": db_stats,
            "system_info": {
                "api_version": "2.0.0",
                "uptime_info": "Available via health endpoint"
            }
        }

    except Exception as e:
        return {
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
