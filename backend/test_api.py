#!/usr/bin/env python3
"""
Test script for the Power Plant Information Extraction API

This script tests the main API endpoints to ensure they work correctly.
"""

import requests
import time
import json
import sys
from typing import Dict, Any

# API Configuration
API_BASE_URL = "http://localhost:8000"
TEST_PLANT_NAME = "Jhajjar Power Plant"

def test_health_check() -> bool:
    """Test the health check endpoint"""
    print("🏥 Testing health check endpoint...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health check passed")
            print(f"   📊 Status: {data.get('status')}")
            print(f"   💾 Database: {data.get('database_status')}")
            print(f"   🔄 Active sessions: {data.get('active_sessions')}")
            return True
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False

def test_debug_endpoints() -> bool:
    """Test debug endpoints"""
    print("🧪 Testing debug endpoints...")
    
    try:
        # Test database connection
        response = requests.get(f"{API_BASE_URL}/api/v1/debug/test-database", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Database test passed")
            print(f"   💾 Connection: {data.get('database_connection')}")
            print(f"   📊 Plant count: {data.get('plant_count')}")
        else:
            print(f"   ⚠️ Database test failed: {response.status_code}")
        
        # Test pipeline components
        response = requests.post(
            f"{API_BASE_URL}/api/v1/debug/test-pipeline",
            params={"plant_name": "Test Plant"},
            timeout=15
        )
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Pipeline components test passed")
            print(f"   📊 Overall status: {data.get('overall_status')}")
        else:
            print(f"   ⚠️ Pipeline components test failed: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Debug endpoints error: {e}")
        return False

def start_pipeline(plant_name: str) -> str:
    """Start pipeline processing and return session ID"""
    print(f"🚀 Starting pipeline for: {plant_name}")
    
    try:
        payload = {
            "plant_name": plant_name,
            "options": {
                "include_images": True,
                "extract_units": True,
                "priority": "normal",
                "max_research_loops": 2,  # Reduced for testing
                "initial_search_query_count": 3  # Reduced for testing
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/pipeline/start",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            session_id = data["session_id"]
            print(f"   ✅ Pipeline started successfully")
            print(f"   🆔 Session ID: {session_id}")
            print(f"   ⏱️ Estimated duration: {data.get('estimated_duration')} seconds")
            return session_id
        else:
            print(f"   ❌ Failed to start pipeline: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Pipeline start error: {e}")
        return None

def monitor_pipeline(session_id: str, max_wait_time: int = 600) -> Dict[str, Any]:
    """Monitor pipeline execution until completion or timeout"""
    print(f"📊 Monitoring pipeline: {session_id}")
    
    start_time = time.time()
    last_phase = ""
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/v1/pipeline/status/{session_id}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                status = data["status"]
                progress = data.get("progress", {})
                current_phase = progress.get("current_phase", "unknown")
                phase_progress = progress.get("current_phase_progress", 0)
                
                # Only print updates when phase changes or significant progress
                if current_phase != last_phase:
                    print(f"   🔄 Phase: {current_phase} ({phase_progress:.1f}%)")
                    last_phase = current_phase
                
                if status in ["completed", "failed", "cancelled"]:
                    print(f"   🏁 Pipeline {status}")
                    return data
                
            else:
                print(f"   ⚠️ Status check failed: {response.status_code}")
            
            time.sleep(10)  # Wait 10 seconds before next check
            
        except Exception as e:
            print(f"   ❌ Monitoring error: {e}")
            time.sleep(5)
    
    print(f"   ⏰ Monitoring timeout after {max_wait_time} seconds")
    return None

def get_results(session_id: str) -> Dict[str, Any]:
    """Get pipeline results"""
    print(f"📋 Getting results for: {session_id}")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/api/v1/pipeline/results/{session_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", {})
            
            print(f"   ✅ Results retrieved successfully")
            print(f"   📊 Processing stats:")
            
            stats = results.get("processing_stats", {})
            for key, value in stats.items():
                print(f"      - {key}: {value}")
            
            s3_urls = results.get("s3_urls", {})
            if s3_urls:
                print(f"   🗂️ S3 URLs created: {len(s3_urls)}")
            
            image_urls = results.get("image_urls", [])
            if image_urls:
                print(f"   🖼️ Images extracted: {len(image_urls)}")
            
            return data
        else:
            print(f"   ❌ Failed to get results: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ Results retrieval error: {e}")
        return None

def test_list_endpoints() -> bool:
    """Test listing endpoints"""
    print("📋 Testing list endpoints...")
    
    try:
        # Test list sessions
        response = requests.get(f"{API_BASE_URL}/api/v1/sessions?limit=5", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Sessions list: {data.get('total_count', 0)} sessions")
        
        # Test list plants
        response = requests.get(f"{API_BASE_URL}/api/v1/plants?page=1&page_size=5", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Plants list: {data.get('total_count', 0)} plants")
        
        # Test metrics
        response = requests.get(f"{API_BASE_URL}/api/v1/metrics", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Metrics retrieved successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ List endpoints error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Power Plant Information Extraction API Test Suite")
    print("=" * 60)
    
    # Test 1: Health check
    if not test_health_check():
        print("❌ Health check failed. Is the API server running?")
        sys.exit(1)
    
    print()
    
    # Test 2: Debug endpoints
    test_debug_endpoints()
    print()
    
    # Test 3: List endpoints
    test_list_endpoints()
    print()
    
    # Test 4: Full pipeline test
    print(f"🔬 Starting full pipeline test with: {TEST_PLANT_NAME}")
    print("-" * 40)
    
    # Start pipeline
    session_id = start_pipeline(TEST_PLANT_NAME)
    if not session_id:
        print("❌ Failed to start pipeline")
        sys.exit(1)
    
    print()
    
    # Monitor pipeline
    final_status = monitor_pipeline(session_id, max_wait_time=300)  # 5 minutes max
    if not final_status:
        print("❌ Pipeline monitoring failed or timed out")
        sys.exit(1)
    
    print()
    
    # Get results if completed
    if final_status.get("status") == "completed":
        results = get_results(session_id)
        if results:
            print("🎉 Full pipeline test completed successfully!")
        else:
            print("⚠️ Pipeline completed but failed to retrieve results")
    else:
        print(f"⚠️ Pipeline ended with status: {final_status.get('status')}")
        if final_status.get("error"):
            error = final_status["error"]
            print(f"   Error: {error.get('error_message')}")
    
    print()
    print("=" * 60)
    print("✅ API test suite completed")

if __name__ == "__main__":
    main()
