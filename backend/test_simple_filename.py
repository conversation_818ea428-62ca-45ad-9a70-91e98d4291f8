#!/usr/bin/env python3
"""
Simple test to verify filename generation logic without S3 dependencies.
"""

def test_organization_filename():
    """Test organization filename generation"""
    org_data = {
        "sk": "org_details",
        "org_uid": "ORG_UN_768B39_52734095"
    }
    
    # This is the logic from the fixed code
    sk_value = org_data.get("sk", "org_details")
    filename = f"{sk_value}.json"
    
    print(f"Organization SK: {sk_value}")
    print(f"Generated filename: {filename}")
    print(f"Expected: org_details.json")
    print(f"✅ Correct: {filename == 'org_details.json'}")
    
    return filename == "org_details.json"

def test_plant_filename():
    """Test plant filename generation"""
    plant_data = {
        "sk": "plant_details",
        "plant_uid": "PLT_768B39_5ABE24_52734095"
    }
    
    # This is the logic from the fixed code
    sk_value = plant_data.get("sk", "plant_unknown")
    filename = f"{sk_value}.json"
    
    print(f"\nPlant SK: {sk_value}")
    print(f"Generated filename: {filename}")
    print(f"Expected: plant_details.json")
    print(f"✅ Correct: {filename == 'plant_details.json'}")
    
    return filename == "plant_details.json"

def test_unit_filename():
    """Test unit filename generation"""
    unit_data = {
        "sk": "unit#coal#1#plant#1",
        "plant_uid": "PLT_768B39_5ABE24_52734095"
    }
    unit_number = 1
    
    # This is the logic from the fixed code
    sk_value = unit_data.get("sk", f"unit_{unit_number}_unknown")
    filename = f"{sk_value}.json"
    
    print(f"\nUnit SK: {sk_value}")
    print(f"Generated filename: {filename}")
    print(f"Expected: unit#coal#1#plant#1.json")
    print(f"✅ Correct: {filename == 'unit#coal#1#plant#1.json'}")
    
    return filename == "unit#coal#1#plant#1.json"

def main():
    print("🧪 Testing filename generation logic...")
    print("=" * 50)
    
    org_ok = test_organization_filename()
    plant_ok = test_plant_filename()
    unit_ok = test_unit_filename()
    
    print("\n" + "=" * 50)
    if org_ok and plant_ok and unit_ok:
        print("✅ All tests passed! Filename fixes are working correctly.")
        print("\n📋 Summary:")
        print("• Organization files: {sk}.json")
        print("• Plant files: {sk}.json") 
        print("• Unit files: {sk}.json")
        print("• Transition plan files: transition_plan.json (unchanged)")
    else:
        print("❌ Some tests failed!")
        
    return org_ok and plant_ok and unit_ok

if __name__ == "__main__":
    main()
