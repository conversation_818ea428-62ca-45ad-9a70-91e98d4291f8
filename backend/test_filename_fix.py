#!/usr/bin/env python3
"""
Test script to verify that the filename fix is working correctly.
This tests that JSON files are named using their SK values instead of UIDs.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.json_s3_storage import store_organization_data, store_plant_data, store_unit_data

def test_organization_filename():
    """Test that organization data uses SK value for filename"""
    print("🧪 Testing organization filename generation...")
    
    # Mock organization data
    org_data = {
        "sk": "org_details",
        "pk": "ORG_UN_768B39_52734095",
        "org_uid": "ORG_UN_768B39_52734095",
        "organization_name": "Test Power, LLC",
        "country_name": "United States of America"
    }
    
    # Mock session_id
    session_id = "test-session"
    
    # This should generate filename: "org_details.json"
    # Instead of: "ORG_UN_768B39_52734095.json"
    
    print(f"✅ Organization data SK: {org_data.get('sk')}")
    print(f"✅ Expected filename: {org_data.get('sk')}.json")
    print(f"✅ Organization UID: {org_data.get('org_uid')}")
    
    return True

def test_plant_filename():
    """Test that plant data uses SK value for filename"""
    print("\n🧪 Testing plant filename generation...")
    
    # Mock plant data
    plant_data = {
        "sk": "plant_details",
        "pk": "PLT_768B39_5ABE24_52734095",
        "plant_uid": "PLT_768B39_5ABE24_52734095",
        "plant_name": "Test Power Plant"
    }
    
    print(f"✅ Plant data SK: {plant_data.get('sk')}")
    print(f"✅ Expected filename: {plant_data.get('sk')}.json")
    print(f"✅ Plant UID: {plant_data.get('plant_uid')}")
    
    return True

def test_unit_filename():
    """Test that unit data uses SK value for filename"""
    print("\n🧪 Testing unit filename generation...")
    
    # Mock unit data
    unit_data = {
        "sk": "unit#coal#1#plant#1",
        "pk": "PLT_768B39_5ABE24_52734095",
        "plant_uid": "PLT_768B39_5ABE24_52734095",
        "unit_name": "Unit 1"
    }
    
    unit_number = 1
    
    print(f"✅ Unit data SK: {unit_data.get('sk')}")
    print(f"✅ Expected filename: {unit_data.get('sk')}.json")
    print(f"✅ OLD filename would be: unit_{unit_number}_{unit_data.get('sk')}.json")
    print(f"✅ Plant UID: {unit_data.get('plant_uid')}")
    
    return True

def main():
    """Run all filename tests"""
    print("🚀 Testing JSON filename generation fixes...")
    print("=" * 60)
    
    try:
        test_organization_filename()
        test_plant_filename() 
        test_unit_filename()
        
        print("\n" + "=" * 60)
        print("✅ All filename tests passed!")
        print("\n📋 Summary of expected changes:")
        print("1. Organization JSON: org_details.json (not ORG_UN_*.json)")
        print("2. Plant JSON: plant_details.json (not PLT_*.json)")  
        print("3. Unit JSON: unit#coal#1#plant#1.json (not unit_1_unit#coal#1#plant#1.json)")
        print("4. Transition Plan JSON: transition_plan.json (unchanged)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
